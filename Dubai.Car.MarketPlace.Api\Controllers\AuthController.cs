﻿using Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Security.Claims;
using System.Threading.Tasks;
using WatchDog;

namespace Dubai.Car.MarketPlace.Api.Controllers
{
    /// <summary>
    /// Controller for user authentication and registration
    /// </summary>
    public class AuthController : BaseController
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        /// <summary>
        /// Auth Constructor
        /// </summary>
        /// <param name="authService"></param>
        /// <param name="logger"></param>
        public AuthController(
            IAuthService authService,
            ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        #region Register
        /// <summary>
        /// Register a new user
        /// </summary>
        /// <param name="model">Registration details</param>
        /// <returns>Registration result</returns>
        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for registration: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "Register");
                return BadRequest(ModelState);
            }

            WatchLogger.Log($"Registration attempt for email: {model.Email}", "Register");
            var result = await _authService.RegisterAsync(model);
            return StatusCode((int)result.StatusCode, result);
        }
        #endregion

        #region Verify Email
        /// <summary>
        /// Verify user email
        /// </summary>
        /// <param name="token">Verification token</param>
        /// <param name="email">User email</param>
        /// <returns>Verification result</returns>
        [HttpGet("verify-email")]
        public async Task<IActionResult> VerifyEmail([FromQuery] string token, [FromQuery] string email)
        {
            if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(email))
            {
                WatchLogger.LogWarning("Email verification attempt with missing token or email", "VerifyEmail");
                return BadRequest(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.BadRequest,
                    Message = ResponseMessages.InvalidVerificationRequestResponse
                });
            }

            WatchLogger.Log($"Email verification attempt for: {email}", "VerifyEmail");
            var result = await _authService.VerifyEmailAsync(token, email);
            return StatusCode((int)result.StatusCode, result);
        }
        #endregion

        #region Login
        /// <summary>
        /// Login user. If email is not verified, a new verification email will be sent.
        /// </summary>
        /// <param name="model">Login credentials</param>
        /// <returns>Login result with JWT token or verification email status</returns>
        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for login: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "Login");
                return BadRequest(ModelState);
            }

            WatchLogger.Log($"Login attempt for email: {model.Email}", "Login");
            var result = await _authService.LoginAsync(model);
            return StatusCode((int)result.StatusCode, result);
        }
        #endregion

        #region Change Password
        /// <summary>
        /// Change user password
        /// </summary>
        /// <param name="model">Change password details</param>
        /// <returns>Change password result</returns>
        [Authorize]
        [HttpPost("change-password")]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning("Invalid model state for change password", "ChangePassword");
                return BadRequest(ModelState);
            }

            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId) || !long.TryParse(userId, out long userIdLong))
            {
                WatchLogger.LogWarning("Invalid user ID in token", "ChangePassword");
                return Unauthorized(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.Unauthorized,
                    Message = ResponseMessages.UnauthorizedResponse
                });
            }

            WatchLogger.Log($"Change password attempt for user ID: {userIdLong}", "ChangePassword");
            var result = await _authService.ChangePasswordAsync(userIdLong, model);
            return StatusCode((int)result.StatusCode, result);
        }
        #endregion

        #region Forgot Password
        /// <summary>
        /// Request password reset
        /// </summary>
        /// <param name="model">Forgot password details</param>
        /// <returns>Forgot password result</returns>
        [HttpPost("forgot-password")]
        public async Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for forgot password: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "ForgotPassword");
                return BadRequest(ModelState);
            }

            WatchLogger.Log($"Forgot password request for email: {model.Email}", "ForgotPassword");
            var result = await _authService.ForgotPasswordAsync(model);
            return StatusCode((int)result.StatusCode, result);
        }
        #endregion

        #region Reset Password
        /// <summary>
        /// Reset password
        /// </summary>
        /// <param name="model">Reset password details</param>
        /// <returns>Reset password result</returns>
        [HttpPost("reset-password")]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for reset password: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "ResetPassword");
                return BadRequest(ModelState);
            }

            WatchLogger.Log($"Reset password attempt for email: {model.Email}", "ResetPassword");
            var result = await _authService.ResetPasswordAsync(model);
            return StatusCode((int)result.StatusCode, result);
        }
        #endregion

        #region Refresh Token
        /// <summary>
        /// Refresh JWT token
        /// </summary>
        /// <param name="model">Refresh token details</param>
        /// <returns>Refresh token result with new JWT token</returns>
        [HttpPost("refresh-token")]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning("Invalid model state for refresh token", "RefreshToken");
                return BadRequest(ModelState);
            }

            WatchLogger.Log("Refresh token attempt", "RefreshToken");
            var result = await _authService.RefreshTokenAsync(model);
            return StatusCode((int)result.StatusCode, result);
        }
        #endregion

        #region Log out
        /// <summary>
        /// Logout user
        /// </summary>
        /// <returns>Logout result</returns>
        [Authorize]
        [HttpPost("logout")]
        public async Task<IActionResult> Logout()
        {
            var token = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
            if (string.IsNullOrEmpty(token))
            {
                WatchLogger.LogWarning("Logout attempt with missing token", "Logout");
                return BadRequest(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.BadRequest,
                    Message = ResponseMessages.InvalidTokenResponse
                });
            }

            WatchLogger.Log("Logout attempt", "Logout");
            var result = await _authService.LogoutAsync(token);
            return StatusCode((int)result.StatusCode, result);
        }
        #endregion
    }
}
