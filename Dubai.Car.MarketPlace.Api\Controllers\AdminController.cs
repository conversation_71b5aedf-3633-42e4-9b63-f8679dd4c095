using Dubai.Car.MarketPlace.Common.DTOs.Requests.Admin;
using Dubai.Car.MarketPlace.Common.DTOs.Responses;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Security.Claims;
using WatchDog;
using static Dubai.Car.MarketPlace.Common.Enums.RoleEnums;

namespace Dubai.Car.MarketPlace.Api.Controllers
{
    /// <summary>
    /// Controller for super admin operations
    /// </summary>
    //[Authorize(Roles = nameof(SystemRoles.SuperAdmin))]
    [Authorize]
    public class AdminController : BaseController
    {
        private readonly IAdminService _adminService;
        private readonly ILogger<AdminController> _logger;

        /// <summary>
        /// Constructor for AdminController
        /// </summary>
        /// <param name="adminService">Admin service</param>
        /// <param name="logger">Logger</param>
        public AdminController(
            IAdminService adminService,
            ILogger<AdminController> logger)
        {
            _adminService = adminService;
            _logger = logger;
        }

        /// <summary>
        /// Create a new admin user
        /// </summary>
        /// <param name="model">Create admin user request</param>
        /// <returns>Created admin user information</returns>
        [HttpPost("create-admin")]
        [ProducesResponseType(typeof(ApiResponse<AdminUserResponseDto>), 201)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 409)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> CreateAdminUser([FromBody] CreateAdminUserRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for admin user creation: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "CreateAdminUser");
                return BadRequest(ModelState);
            }

            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            WatchLogger.Log($"Creating admin user: {model.Email} by super admin: {currentUserId}", "CreateAdminUser");

            var result = await _adminService.CreateAdminUserAsync(model);

            if (result.IsSuccess)
            {
                return CreatedAtAction(nameof(GetAdminUserById), new { userId = result.Data!.Id }, result);
            }

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Update an existing admin user
        /// </summary>
        /// <param name="userId">User ID to update</param>
        /// <param name="model">Update admin user request</param>
        /// <returns>Updated admin user information</returns>
        [HttpPut("update-admin/{userId}")]
        [ProducesResponseType(typeof(ApiResponse<AdminUserResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> UpdateAdminUser(Guid userId, [FromBody] UpdateAdminUserRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for admin user update: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "UpdateAdminUser");
                return BadRequest(ModelState);
            }

            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            WatchLogger.Log($"Updating admin user: {userId} by super admin: {currentUserId}", "UpdateAdminUser");

            var result = await _adminService.UpdateAdminUserAsync(userId, model);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Delete an admin user
        /// </summary>
        /// <param name="userId">User ID to delete</param>
        /// <returns>Delete operation result</returns>
        [HttpDelete("delete-admin/{userId}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> DeleteAdminUser(Guid userId)
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            WatchLogger.Log($"Deleting admin user: {userId} by super admin: {currentUserId}", "DeleteAdminUser");

            var result = await _adminService.DeleteAdminUserAsync(userId);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get an admin user by ID
        /// </summary>
        /// <param name="userId">User ID to retrieve</param>
        /// <returns>Admin user information</returns>
        [HttpGet("get-admin/{userId}")]
        [ProducesResponseType(typeof(ApiResponse<AdminUserResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetAdminUserById(Guid userId)
        {
            var result = await _adminService.GetAdminUserByIdAsync(userId);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get all admin users with pagination
        /// </summary>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>List of admin users</returns>
        [HttpGet("get-all-admins")]
        [ProducesResponseType(typeof(ApiResponse<List<AdminUserResponseDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetAllAdminUsers([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            if (page < 1 || pageSize < 1 || pageSize > 100)
            {
                WatchLogger.LogWarning($"Invalid pagination parameters: page={page}, pageSize={pageSize}", "GetAllAdminUsers");
                return BadRequest(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.BadRequest,
                    Message = "Invalid pagination parameters. Page must be >= 1 and pageSize must be between 1 and 100."
                });
            }

            var result = await _adminService.GetAllAdminUsersAsync(page, pageSize);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Lock or unlock an admin user
        /// </summary>
        /// <param name="userId">User ID to lock/unlock</param>
        /// <param name="model">Lock admin user request</param>
        /// <returns>Lock operation result</returns>
        [HttpPut("lock-admin/{userId}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> LockUnlockAdminUser(Guid userId, [FromBody] LockAdminUserRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for admin user lock/unlock: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "LockUnlockAdminUser");
                return BadRequest(ModelState);
            }

            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var action = model.IsLocked ? "locking" : "unlocking";
            WatchLogger.Log($"{action} admin user: {userId} by super admin: {currentUserId}", "LockUnlockAdminUser");

            var result = await _adminService.LockUnlockAdminUserAsync(userId, model);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get all available permissions
        /// </summary>
        /// <returns>List of available permissions</returns>
        [HttpGet("get-permissions")]
        [ProducesResponseType(typeof(ApiResponse<List<PermissionResponseDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetAllPermissions()
        {
            var result = await _adminService.GetAllPermissionsAsync();

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Assign or remove permissions from a role
        /// </summary>
        /// <param name="model">Assign role permissions request</param>
        /// <returns>Assignment operation result</returns>
        [HttpPut("assign-role-permissions")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> AssignRolePermissions([FromBody] AssignRolePermissionsRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for role permissions assignment: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "AssignRolePermissions");
                return BadRequest(ModelState);
            }

            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            WatchLogger.Log($"Assigning role permissions for role: {model.RoleId} by super admin: {currentUserId}", "AssignRolePermissions");

            var result = await _adminService.AssignRolePermissionsAsync(model);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get all roles with their permissions
        /// </summary>
        /// <returns>List of roles with permissions</returns>
        [HttpGet("get-roles-with-permissions")]
        [ProducesResponseType(typeof(ApiResponse<List<RoleWithPermissionsResponseDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetAllRolesWithPermissions()
        {
            var result = await _adminService.GetAllRolesWithPermissionsAsync();

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get a role by ID with its permissions
        /// </summary>
        /// <param name="roleId">Role ID to retrieve</param>
        /// <returns>Role with permissions</returns>
        [HttpGet("get-role-with-permissions/{roleId}")]
        [ProducesResponseType(typeof(ApiResponse<RoleWithPermissionsResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetRoleByIdWithPermissions(Guid roleId)
        {
            var result = await _adminService.GetRoleByIdWithPermissionsAsync(roleId);

            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get complete dashboard data including stats, vendor applications, support tickets, and revenue
        /// </summary>
        /// <returns>Complete dashboard data</returns>
        [HttpGet("dashboard")]
        [ProducesResponseType(typeof(ApiResponse<DashboardResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetDashboardData()
        {
            var result = await _adminService.GetDashboardDataAsync();
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get dashboard statistics only (total users, active vendors, listings, revenue)
        /// </summary>
        /// <returns>Dashboard statistics</returns>
        [HttpGet("dashboard/stats")]
        [ProducesResponseType(typeof(ApiResponse<DashboardStatsResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetDashboardStats()
        {
            var result = await _adminService.GetDashboardStatsAsync();
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get pending vendor applications for dashboard
        /// </summary>
        /// <param name="count">Number of applications to return (default: 5)</param>
        /// <returns>Pending vendor applications</returns>
        [HttpGet("dashboard/pending-vendor-applications")]
        [ProducesResponseType(typeof(ApiResponse<List<DashboardVendorApplicationResponseDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetPendingVendorApplications([FromQuery] int count = 5)
        {
            if (count < 1 || count > 50)
            {
                return BadRequest(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.BadRequest,
                    Message = "Count must be between 1 and 50."
                });
            }

            var result = await _adminService.GetPendingVendorApplicationsAsync(count);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get platform revenue data for dashboard
        /// </summary>
        /// <returns>Platform revenue data</returns>
        [HttpGet("dashboard/revenue")]
        [ProducesResponseType(typeof(ApiResponse<DashboardRevenueResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 403)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetPlatformRevenueData()
        {
            var result = await _adminService.GetPlatformRevenueDataAsync();
            return StatusCode((int)result.StatusCode, result);
        }
    }
}