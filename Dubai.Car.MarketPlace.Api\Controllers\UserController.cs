﻿using Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Security.Claims;
using System.Threading.Tasks;
using WatchDog;

namespace Dubai.Car.MarketPlace.Api.Controllers
{
    /// <summary>
    /// User Controller
    /// </summary>
    [Authorize]
    public class UserController : BaseController
    {
        private readonly IUserService _userService;
        private readonly ILogger<UserController> _logger;

        /// <summary>
        /// Constructor for UserController
        /// </summary>
        /// <param name="userService"></param>
        /// <param name="logger"></param>
        public UserController(
            IUserService userService,
            ILogger<UserController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        /// <summary>
        /// Get user profile by ID
        /// </summary>
        /// <param name="userId">ID of the user to retrieve</param>
        /// <returns>User profile information</returns>
        [HttpGet("get-user/{userId}")]
        [ProducesResponseType(typeof(ApiResponse<UserProfileResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetUserById(long userId)
        {
            WatchLogger.Log($"Get user profile request for user ID: {userId}", "GetUserById");
            var result = await _userService.GetUserProfileAsync(userId);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get current user's profile
        /// </summary>
        /// <returns>User profile information</returns>
        [HttpGet("get-profile")]
        [ProducesResponseType(typeof(ApiResponse<UserProfileResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetUserProfile()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId) || !long.TryParse(userId, out long userIdLong))
            {
                WatchLogger.LogWarning("Invalid user ID in token", "GetUserProfile");
                return Unauthorized(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.Unauthorized,
                    Message = ResponseMessages.UnauthorizedResponse
                });
            }

            WatchLogger.Log($"Get profile request for user ID: {userIdLong}", "GetUserProfile");
            var result = await _userService.GetUserProfileAsync(userIdLong);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Update current user's profile
        /// </summary>
        /// <param name="model">Updated profile information</param>
        /// <returns>Updated user profile</returns>
        [HttpPut("update-profile")]
        [ProducesResponseType(typeof(ApiResponse<UserProfileResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> UpdateUserProfile([FromBody] UpdateUserProfileRequestDto model)
        {
            if (!ModelState.IsValid)
            {
                WatchLogger.LogWarning($"Invalid model state for profile update: {string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))}", "UpdateUserProfile");
                return BadRequest(ModelState);
            }

            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId) || !long.TryParse(userId, out long userIdLong))
            {
                WatchLogger.LogWarning("Invalid user ID in token", "UpdateUserProfile");
                return Unauthorized(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.Unauthorized,
                    Message = ResponseMessages.UnauthorizedResponse
                });
            }

            WatchLogger.Log($"Update profile request for user ID: {userIdLong}", "UpdateUserProfile");
            var result = await _userService.UpdateUserProfileAsync(userIdLong, model);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get all users with pagination
        /// </summary>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>Paginated list of users</returns>
        [HttpGet("get-all-users")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(typeof(ApiResponse<UserProfileResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetAllUsers([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            WatchLogger.Log($"Get all users request. Page: {pageNumber}, Size: {pageSize}", "GetAllUsers");
            var result = await _userService.GetAllUsersAsync(pageNumber, pageSize);
            return StatusCode((int)result.StatusCode, result);
        }

        /// <summary>
        /// Get current user's permissions
        /// </summary>
        /// <returns>User permissions information</returns>
        [HttpGet("get-permissions")]
        [ProducesResponseType(typeof(ApiResponse<UserPermissionsResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse), 400)]
        [ProducesResponseType(typeof(ApiResponse), 401)]
        [ProducesResponseType(typeof(ApiResponse), 404)]
        [ProducesResponseType(typeof(ApiResponse), 500)]
        public async Task<IActionResult> GetUserPermissions()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId) || !long.TryParse(userId, out long userIdLong))
            {
                WatchLogger.LogWarning("Invalid user ID in token", "GetUserPermissions");
                return Unauthorized(new ApiResponse
                {
                    IsSuccess = false,
                    StatusCode = HttpStatusCode.Unauthorized,
                    Message = ResponseMessages.UnauthorizedResponse
                });
            }

            WatchLogger.Log($"Get permissions request for user ID: {userIdLong}", "GetUserPermissions");
            var result = await _userService.GetUserPermissionsAsync(userIdLong);
            return StatusCode((int)result.StatusCode, result);
        }
    }
}
