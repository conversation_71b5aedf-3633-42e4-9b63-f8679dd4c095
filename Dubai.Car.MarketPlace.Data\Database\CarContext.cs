﻿using Dubai.Car.MarketPlace.Data.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Dubai.Car.MarketPlace.Data.Database
{
    public class CarContext : IdentityDbContext<User, Role, Guid>
    {
        public CarContext(DbContextOptions<CarContext> options)
            : base(options)
        {
        }        // Db Sets for your entities can be defined here

        public DbSet<BlackListedToken> BlackListedTokens { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<VendorApplication> VendorApplications { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            // Additional model configurations can go here

            // rename IdentityUserRole to UserRole to avoid conflicts with IdentityDbContext
            modelBuilder.Entity<User>().ToTable("Users");
            modelBuilder.Entity<Role>().ToTable("Roles");
            modelBuilder.Entity<IdentityUserRole<Guid>>().ToTable("UserRoles");
            modelBuilder.Entity<IdentityUserClaim<Guid>>().ToTable("UserClaims");
            modelBuilder.Entity<IdentityUserLogin<Guid>>().ToTable("UserLogins");
            modelBuilder.Entity<IdentityRoleClaim<Guid>>().ToTable("RoleClaims");

            // Configure composite primary key for UserPermission
            modelBuilder.Entity<UserPermission>()
                .HasKey(up => new { up.UserId, up.Permission });
            
        }

        public override int SaveChanges()
        {
            ApplyEntityTracking();
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            ApplyEntityTracking();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void ApplyEntityTracking()
        {
            foreach (var entry in ChangeTracker.Entries())
            {
                if (entry.Entity is BaseEntity baseEntity)
                {
                    var now = DateTime.UtcNow;

                    switch (entry.State)
                    {
                        case EntityState.Added:
                            baseEntity.CreatedOn = now;
                            baseEntity.ModifiedOn = null;
                            baseEntity.IsDeleted = false;
                            break;

                        case EntityState.Modified:
                            baseEntity.ModifiedOn = now;
                            break;

                        case EntityState.Deleted:
                            // Soft delete handling
                            entry.State = EntityState.Modified;
                            baseEntity.IsDeleted = true;
                            baseEntity.DeletedOn = now;
                            break;
                    }
                }
            }
        }
    }
}
