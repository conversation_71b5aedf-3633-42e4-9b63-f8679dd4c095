using static Dubai.Car.MarketPlace.Common.Enums.VendorEnums;

namespace Dubai.Car.MarketPlace.Common.DTOs.Responses.Vendor
{
    /// <summary>
    /// DTO for vendor application response
    /// </summary>
    public class VendorApplicationResponseDto
    {
        /// <summary>
        /// The ID of the vendor application
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// The business name of the vendor
        /// </summary>
        public string BusinessName { get; set; } = default!;
        
        /// <summary>
        /// The contact person's name
        /// </summary>
        public string ContactPersonName { get; set; } = default!;
        
        /// <summary>
        /// The position of the contact person
        /// </summary>
        public string Position { get; set; } = default!;
        
        /// <summary>
        /// The business email
        /// </summary>
        public string BusinessEmail { get; set; } = default!;
        
        /// <summary>
        /// The phone number
        /// </summary>
        public string PhoneNumber { get; set; } = default!;
        
        /// <summary>
        /// The trade license number
        /// </summary>
        public string TradeLicenseNumber { get; set; } = default!;
        
        /// <summary>
        /// The business address
        /// </summary>
        public string BusinessAddress { get; set; } = default!;
        
        /// <summary>
        /// The city where the business is located
        /// </summary>
        public string City { get; set; } = default!;
        
        /// <summary>
        /// The business specialization (car brands)
        /// </summary>
        public string? Specialization { get; set; }
        
        /// <summary>
        /// The business description
        /// </summary>
        public string? BusinessDescription { get; set; }
        
        /// <summary>
        /// The status of the vendor application
        /// </summary>
        public VendorApplicationStatus Status { get; set; }
        
        /// <summary>
        /// The status of the vendor application as a string
        /// </summary>
        public string StatusText => Status.ToString();
        
        /// <summary>
        /// The date when the application was submitted
        /// </summary>
        public DateTime SubmissionDate { get; set; }
        
        /// <summary>
        /// The date when the application was reviewed
        /// </summary>
        public DateTime? ReviewDate { get; set; }
        
        /// <summary>
        /// The ID of the admin who reviewed the application
        /// </summary>
        public Guid? ReviewedByUserId { get; set; }
        
        /// <summary>
        /// The name of the admin who reviewed the application
        /// </summary>
        public string? ReviewerName { get; set; }
        
        /// <summary>
        /// Comments or notes about the application review
        /// </summary>
        public string? ReviewComments { get; set; }
        
        /// <summary>
        /// URL to access the trade license document
        /// </summary>
        public string? TradeLicenseDocumentUrl { get; set; }
        
        /// <summary>
        /// URLs to access any additional documents
        /// </summary>
        public List<DocumentResponseDto>? AdditionalDocuments { get; set; }
    }
    
    /// <summary>
    /// DTO for document response
    /// </summary>
    public class DocumentResponseDto
    {
        /// <summary>
        /// URL to access the document
        /// </summary>
        public string Url { get; set; } = default!;
        
        /// <summary>
        /// Document filename
        /// </summary>
        public string Filename { get; set; } = default!;
        
        /// <summary>
        /// Document description
        /// </summary>
        public string? Description { get; set; }
    }
}
