﻿using AutoMapper;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.Admin;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.User;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.Vendor;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Admin;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Vendor;
using Dubai.Car.MarketPlace.Data.Entities;

namespace Dubai.Car.MarketPlace.Logic.Helper
{
    public class AutoMapper : Profile
    {
        public AutoMapper()
        {
            // Map from RegisterReqModel to User
            CreateMap<RegisterReqModel, User>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.Email))
                .ReverseMap();
            CreateMap<UserResModel, User>().ReverseMap();
            CreateMap<RegisterRequestDto, User>().ReverseMap();

            CreateMap<User, AuthResponseDto>()
                .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.UserType, opt => opt.MapFrom(src => src.UserType.ToString()))
                .ReverseMap();

            // Vendor-related mappings
            CreateMap<VendorApplicationRequestDto, VendorApplication>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Status, opt => opt.Ignore())
                .ForMember(dest => dest.SubmissionDate, opt => opt.Ignore())
                .ForMember(dest => dest.ReviewDate, opt => opt.Ignore())
                .ForMember(dest => dest.ReviewedByUserId, opt => opt.Ignore())
                .ForMember(dest => dest.ReviewedByUser, opt => opt.Ignore())
                .ForMember(dest => dest.ReviewComments, opt => opt.Ignore())
                .ForMember(dest => dest.TradeLicenseDocumentKey, opt => opt.Ignore())
                .ForMember(dest => dest.AdditionalDocumentKeys, opt => opt.Ignore())
                .ForMember(dest => dest.UserId, opt => opt.Ignore())
                .ForMember(dest => dest.User, opt => opt.Ignore());

            CreateMap<VendorApplication, VendorApplicationResponseDto>()
                .ForMember(dest => dest.ReviewerName, opt => opt.Ignore())
                .ForMember(dest => dest.TradeLicenseDocumentUrl, opt => opt.Ignore())
                .ForMember(dest => dest.AdditionalDocuments, opt => opt.Ignore());

            CreateMap<VendorApplication, User>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.UserName, opt => opt.Ignore())
                .ForMember(dest => dest.Email, opt => opt.Ignore())
                .ForMember(dest => dest.FirstName, opt => opt.Ignore())
                .ForMember(dest => dest.LastName, opt => opt.Ignore())
                .ForMember(dest => dest.Gender, opt => opt.Ignore())
                .ForMember(dest => dest.PhoneNumber, opt => opt.Ignore())
                .ForMember(dest => dest.Address, opt => opt.Ignore())
                .ForMember(dest => dest.State, opt => opt.Ignore())
                .ForMember(dest => dest.EmailConfirmed, opt => opt.Ignore())
                .ForMember(dest => dest.IsActive, opt => opt.Ignore());

            // Admin-related mappings
            CreateMap<User, AdminUserResponseDto>()
                .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => $"{src.FirstName} {src.LastName}"))
                .ForMember(dest => dest.Role, opt => opt.Ignore())
                .ForMember(dest => dest.RoleId, opt => opt.Ignore())
                .ForMember(dest => dest.IsLocked, opt => opt.Ignore());

            CreateMap<CreateAdminUserRequestDto, User>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.NormalizedUserName, opt => opt.MapFrom(src => src.Email.ToUpper()))
                .ForMember(dest => dest.NormalizedEmail, opt => opt.MapFrom(src => src.Email.ToUpper()))
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.EmailConfirmed, opt => opt.Ignore())
                .ForMember(dest => dest.UserType, opt => opt.Ignore())
                .ForMember(dest => dest.IsActive, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore());
        }
    }
}
