﻿using Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;

namespace Dubai.Car.MarketPlace.Logic.Services.Contracts
{
    public interface IAuthService
    {
        Task<ApiResponse<AuthResponseDto>> RegisterAsync(RegisterRequestDto model);
        Task<ApiResponse<string>> VerifyEmailAsync(string token, string email);
        Task<ApiResponse<AuthResponseDto>> LoginAsync(LoginRequestDto model);
        Task<ApiResponse<string>> ChangePasswordAsync(long userId, ChangePasswordRequestDto model);
        Task<ApiResponse<string>> ForgotPasswordAsync(ForgotPasswordRequestDto model);
        Task<ApiResponse<string>> ResetPasswordAsync(ResetPasswordRequestDto model);
        Task<ApiResponse<AuthResponseDto>> RefreshTokenAsync(RefreshTokenRequestDto model);
        Task<ApiResponse<string>> LogoutAsync(string token);

        // UAE PASS Authentication Methods
        Task<ApiResponse<UaePassAuthUrlResponseDto>> GetUaePassAuthorizationUrlAsync();
        Task<ApiResponse<AuthResponseDto>> HandleUaePassCallbackAsync(UaePassCallbackRequestDto model);
    }
}
