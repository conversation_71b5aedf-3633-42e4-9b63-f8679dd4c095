using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using System.Text.RegularExpressions;

namespace Dubai.Car.MarketPlace.Common.Utility
{
    // Custom IFormFile implementation for .NET 8.0 compatibility
    public class CustomFormFile : IFormFile
    {
        private readonly Stream _stream;
        private readonly long _length;

        public CustomFormFile(Stream stream, long length, string name, string fileName)
        {
            _stream = stream;
            _length = length;
            Name = name;
            FileName = fileName;
            Headers = new HeaderDictionary();
        }

        public string ContentType { get; set; } = "application/octet-stream";
        public string ContentDisposition { get; set; } = string.Empty;
        public IHeaderDictionary Headers { get; set; }
        public long Length => _length;
        public string Name { get; set; }
        public string FileName { get; set; }

        public void CopyTo(Stream target)
        {
            _stream.Position = 0;
            _stream.CopyTo(target);
        }

        public async Task CopyToAsync(Stream target, CancellationToken cancellationToken = default)
        {
            _stream.Position = 0;
            await _stream.CopyToAsync(target, cancellationToken);
        }

        public Stream OpenReadStream()
        {
            _stream.Position = 0;
            return _stream;
        }
    }

    // Simple HeaderDictionary implementation
    public class HeaderDictionary : Dictionary<string, StringValues>, IHeaderDictionary
    {
        public long? ContentLength { get; set; }
    }

    public static class FileHelper
    {
        /// <summary>
        /// Converts a base64 string to an IFormFile
        /// </summary>
        /// <param name="base64String">The base64 string to convert</param>
        /// <param name="fileName">The name to give the file</param>
        /// <returns>An IFormFile created from the base64 string</returns>
        public static IFormFile ConvertBase64ToIFormFile(string base64String, string fileName)
        {
            // Check if the base64 string is valid
            if (string.IsNullOrEmpty(base64String))
            {
                throw new ArgumentException("Base64 string is null or empty");
            }

            // Remove data:image/png;base64, or similar prefixes if present
            if (base64String.Contains(","))
            {
                base64String = base64String.Split(',')[1];
            }

            // Validate base64 string
            if (!IsValidBase64(base64String))
            {
                throw new ArgumentException("Invalid base64 string");
            }

            // Convert base64 string to byte array
            byte[] fileBytes;
            try
            {
                fileBytes = Convert.FromBase64String(base64String);
            }
            catch (FormatException)
            {
                throw new ArgumentException("Invalid base64 string format");
            }

            // Validate file size (empty file)
            if (fileBytes.Length == 0)
            {
                throw new ArgumentException("File is empty");
            }

            // Create a memory stream from the byte array
            var stream = new MemoryStream(fileBytes);

            // Determine content type based on file extension
            string contentType = GetContentTypeFromFileName(fileName);

            // Create and return the form file using a custom implementation
            return new CustomFormFile(stream, fileBytes.Length, "file", fileName)
            {
                ContentType = contentType
            };
        }

        /// <summary>
        /// Checks if a string is valid base64
        /// </summary>
        /// <param name="base64">The string to check</param>
        /// <returns>True if the string is valid base64, false otherwise</returns>
        private static bool IsValidBase64(string base64)
        {
            if (string.IsNullOrEmpty(base64))
                return false;

            // Check if the string contains only valid base64 characters
            return Regex.IsMatch(base64, @"^[a-zA-Z0-9\+/]*={0,3}$", RegexOptions.None);
        }

        /// <summary>
        /// Gets the content type based on the file extension
        /// </summary>
        /// <param name="fileName">The file name</param>
        /// <returns>The content type</returns>
        private static string GetContentTypeFromFileName(string fileName)
        {
            string extension = Path.GetExtension(fileName).ToLowerInvariant();

            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".webp" => "image/webp",
                ".svg" => "image/svg+xml",
                _ => "application/octet-stream" // Default content type
            };
        }
    }
}
