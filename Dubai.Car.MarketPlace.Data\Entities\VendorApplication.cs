using static Dubai.Car.MarketPlace.Common.Enums.VendorEnums;

namespace Dubai.Car.MarketPlace.Data.Entities
{    /// <summary>
    /// Represents a vendor application in the system.
    /// </summary>
    public class VendorApplication
    {
        /// <summary>
        /// The unique ID of the application
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// The business name of the vendor
        /// </summary>
        public string BusinessName { get; set; } = default!;
        
        /// <summary>
        /// The contact person's name
        /// </summary>
        public string ContactPersonName { get; set; } = default!;
        
        /// <summary>
        /// The position of the contact person
        /// </summary>
        public string Position { get; set; } = default!;
        
        /// <summary>
        /// The business email
        /// </summary>
        public string BusinessEmail { get; set; } = default!;
        
        /// <summary>
        /// The phone number
        /// </summary>
        public string PhoneNumber { get; set; } = default!;
        
        /// <summary>
        /// The trade license number
        /// </summary>
        public string TradeLicenseNumber { get; set; } = default!;
        
        /// <summary>
        /// The business address
        /// </summary>
        public string BusinessAddress { get; set; } = default!;
        
        /// <summary>
        /// The city where the business is located
        /// </summary>
        public string City { get; set; } = default!;
        
        /// <summary>
        /// The business specialization (car brands)
        /// </summary>
        public string? Specialization { get; set; }
        
        /// <summary>
        /// The business description
        /// </summary>
        public string? BusinessDescription { get; set; }
        
        /// <summary>
        /// The status of the vendor application
        /// </summary>
        public VendorApplicationStatus Status { get; set; } = VendorApplicationStatus.Pending;
        
        /// <summary>
        /// The date when the application was submitted
        /// </summary>
        public DateTime SubmissionDate { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// The date when the application was reviewed
        /// </summary>
        public DateTime? ReviewDate { get; set; }
        
        /// <summary>
        /// The ID of the admin who reviewed the application
        /// </summary>
        public Guid? ReviewedByUserId { get; set; }
        
        /// <summary>
        /// The admin who reviewed the application
        /// </summary>
        public virtual User? ReviewedByUser { get; set; }
        
        /// <summary>
        /// Comments or notes about the application review
        /// </summary>
        public string? ReviewComments { get; set; }
        
        /// <summary>
        /// The ID of the user associated with this vendor application
        /// </summary>
        public Guid? UserId { get; set; }
        
        /// <summary>
        /// The user associated with this vendor application
        /// </summary>
        public virtual User? User { get; set; }
        
        /// <summary>
        /// Trade license document key for storage (S3, etc.)
        /// </summary>
        public string? TradeLicenseDocumentKey { get; set; }
        
        /// <summary>
        /// Additional document keys for storage (S3, etc.) - JSON array
        /// </summary>
        public string? AdditionalDocumentKeys { get; set; }
        
        /// <summary>
        /// The subscription plan selected after approval
        /// </summary>
        public string? SelectedSubscriptionPlan { get; set; }
        
        /// <summary>
        /// Flag indicating if this record has been soft deleted
        /// </summary>
        public bool IsDeleted { get; set; } = false;
        
        /// <summary>
        /// The date when the record was created
        /// </summary>
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// The date when the record was last modified
        /// </summary>
        public DateTime? ModifiedOn { get; set; }
        
        /// <summary>
        /// The date when the record was deleted (if applicable)
        /// </summary>
        public DateTime? DeletedOn { get; set; }
    }
}
