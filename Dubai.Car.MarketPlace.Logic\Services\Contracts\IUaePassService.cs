using Dubai.Car.MarketPlace.Common.DTOs.Responses.Auth;
using Dubai.Car.MarketPlace.Common.Utility;

namespace Dubai.Car.MarketPlace.Logic.Services.Contracts
{
    /// <summary>
    /// Interface for UAE PASS authentication service
    /// </summary>
    public interface IUaePassService
    {
        /// <summary>
        /// Generates the authorization URL for UAE PASS login
        /// </summary>
        /// <param name="state">State parameter for CSRF protection</param>
        /// <returns>Authorization URL response</returns>
        Task<ApiResponse<UaePassAuthUrlResponseDto>> GenerateAuthorizationUrlAsync(string? state = null);

        /// <summary>
        /// Exchanges authorization code for access token
        /// </summary>
        /// <param name="code">Authorization code from UAE PASS</param>
        /// <param name="state">State parameter for verification</param>
        /// <returns>Token response from UAE PASS</returns>
        Task<ApiResponse<UaePassTokenResponseDto>> ExchangeCodeForTokenAsync(string code, string? state = null);

        /// <summary>
        /// Retrieves user profile information from UAE PASS
        /// </summary>
        /// <param name="accessToken">Access token from UAE PASS</param>
        /// <returns>User profile information</returns>
        Task<ApiResponse<UaePassUserProfileDto>> GetUserProfileAsync(string accessToken);

        /// <summary>
        /// Validates the state parameter for CSRF protection
        /// </summary>
        /// <param name="state">State parameter to validate</param>
        /// <returns>True if state is valid, false otherwise</returns>
        bool ValidateState(string? state);

        /// <summary>
        /// Generates a secure state parameter for CSRF protection
        /// </summary>
        /// <returns>Generated state parameter</returns>
        string GenerateState();
    }
}
