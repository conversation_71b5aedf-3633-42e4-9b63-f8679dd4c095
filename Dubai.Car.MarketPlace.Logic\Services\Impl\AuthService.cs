﻿using AutoMapper;
using Dubai.Car.MarketPlace.Common.DTOs.Requests.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.Auth;
using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.Utility;
using Dubai.Car.MarketPlace.Data.Entities;
using Dubai.Car.MarketPlace.Logic.Services.Contracts;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Security.Claims;
using System.Text;
using WatchDog;
using Dubai.Car.MarketPlace.Common.Enums;
using Microsoft.EntityFrameworkCore;

namespace Dubai.Car.MarketPlace.Logic.Services.Impl
{
    public class AuthService : IAuthService
    {
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<Role> _roleManager;
        private readonly IEmailService _emailService;
        private readonly ILogger<AuthService> _logger;
        private readonly IMapper _mapper;
        private readonly IJwtService _jwtService;
        private readonly IUaePassService _uaePassService;

        public AuthService(
            UserManager<User> userManager,
            RoleManager<Role> roleManager,
            IEmailService emailService,
            ILogger<AuthService> logger,
            IMapper mapper,
            IJwtService jwtService,
            IUaePassService uaePassService)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _emailService = emailService;
            _logger = logger;
            _mapper = mapper;
            _jwtService = jwtService;
            _uaePassService = uaePassService;
        }

        #region Register An Account
        public async Task<ApiResponse<AuthResponseDto>> RegisterAsync(RegisterRequestDto model)
        {
            var existingUser = await _userManager.FindByEmailAsync(model.Email);
            if (existingUser != null)
            {
                return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.UserAlreadyExistsResponse, HttpStatusCode.BadRequest);
            }

            if (model.Password != model.ConfirmPassword)
            {
                return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.PasswordsDoNotMatchResponse, HttpStatusCode.BadRequest);
            }

            var user = _mapper.Map<User>(model);
            user.UserName = model.Email;
            user.UserType = Common.Enums.AuthEnums.UserTypes.Customer;

            var result = await _userManager.CreateAsync(user, model.Password);
            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                return ApiResponse<AuthResponseDto>.Failed(errors, HttpStatusCode.BadRequest);
            }

            // Add user to Customer role
            var roleResult = await AddUserToRoleAsync(user, RoleEnums.SystemRoles.Customer);
            if (!roleResult.Succeeded)
            {
                _logger.LogWarning("Failed to add user to Customer role: {Email}", user.Email);
                WatchLogger.LogWarning($"Failed to add user to Customer role: {user.Email}", "RegisterAsync");
                // Note: We don't fail the registration if role assignment fails
            }

            var token = await _userManager.GenerateEmailConfirmationTokenAsync(user);
            var encodedToken = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(token));

            var emailSent = await _emailService.SendAccountVerificationEmailAsync(user.Email, encodedToken, user.FirstName);

            if (!emailSent)
            {
                _logger.LogWarning("Failed to send verification email to {Email}", user.Email);
                WatchLogger.LogWarning($"Failed to send verification email to {user.Email}", "RegisterAsync");
            }

            var response = _mapper.Map<AuthResponseDto>(user);
            return ApiResponse<AuthResponseDto>.Success(response, ResponseMessages.RegistrationSuccessResponse);
        }
        #endregion

        #region VerifyEmail/Comfirm Account
        public async Task<ApiResponse<string>> VerifyEmailAsync(string token, string email)
        {
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
            {
                return ApiResponse<string>.Failed(ResponseMessages.InvalidVerificationRequestResponse, HttpStatusCode.BadRequest);
            }

            if (user.EmailConfirmed)
            {
                return ApiResponse<string>.Success(ResponseMessages.EmailAlreadyVerifiedResponse);
            }

            var decodedToken = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(token));
            var result = await _userManager.ConfirmEmailAsync(user, decodedToken);

            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                WatchLogger.LogWarning($"Email verification failed for {email}: {errors}", "VerifyEmailAsync");
                return ApiResponse<string>.Failed(errors, HttpStatusCode.BadRequest);
            }

            _logger.LogInformation("Email verified successfully for {Email}", email);
            WatchLogger.Log($"Email verified successfully: {email}", "VerifyEmailAsync");
            return ApiResponse<string>.Success(ResponseMessages.EmailVerificationSuccessResponse);
        }
        #endregion

        #region Login
        public async Task<ApiResponse<AuthResponseDto>> LoginAsync(LoginRequestDto model)
        {
            var user = await _userManager.FindByEmailAsync(model.Email);
            if (user == null)
            {
                WatchLogger.LogWarning($"Login attempt failed for non-existent email: {model.Email}", "LoginAsync");
                return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.InvalidCredentialsResponse, HttpStatusCode.BadRequest);
            }

            if (!user.EmailConfirmed)
            {
                WatchLogger.LogWarning($"Login attempt for unverified email: {model.Email}", "LoginAsync");

                // Generate a new verification token and send email
                var verificationToken = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                var encodedToken = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(verificationToken));

                var emailSent = await _emailService.SendAccountVerificationEmailAsync(user.Email, encodedToken, user.FirstName);

                if (emailSent)
                {
                    return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.EmailVerificationResentResponse, HttpStatusCode.BadRequest);
                }
                else
                {
                    _logger.LogWarning("Failed to send verification email during login attempt for {Email}", user.Email);
                    WatchLogger.LogError($"Failed to send verification email during login attempt for {user.Email}", "LoginAsync");
                    return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.AccountNotVerifiedResponse, HttpStatusCode.BadRequest);
                }
            }

            var isPasswordValid = await _userManager.CheckPasswordAsync(user, model.Password);
            if (!isPasswordValid)
            {
                WatchLogger.LogWarning($"Login attempt with invalid password for: {model.Email}", "LoginAsync");
                return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.InvalidCredentialsResponse, HttpStatusCode.BadRequest);
            }

            var token = await _jwtService.GenerateJwtToken(user);
            var refreshToken = await _jwtService.GenerateRefreshToken();

            user.RefreshToken = refreshToken;
            user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7); // Refresh token valid for 7 days
            await _userManager.UpdateAsync(user);

            var response = _mapper.Map<AuthResponseDto>(user);
            response.Token = token;
            response.RefreshToken = refreshToken;
            response.RefreshTokenExpiryTime = user.RefreshTokenExpiryTime;

            // Get user's role and add it to the response
            var roles = await _userManager.GetRolesAsync(user);
            response.Role = roles.FirstOrDefault() ?? string.Empty;

            // Get user's permissions and add them to the response
            var permissionsEnum = await _jwtService.GetUserPermissionsEnumAsync(user);
            if (permissionsEnum != null)
                permissionsEnum.ForEach(x =>
                {
                    response.Permissions.Add(x.ToString());
                });

            _logger.LogInformation("User logged in successfully: {Email} with role: {Role} and {PermissionCount} permissions", user.Email, response.Role, response.Permissions.Count);
            WatchLogger.Log($"User logged in successfully: {user.Email} with role: {response.Role} and {response.Permissions.Count} permissions", "LoginAsync");
            return ApiResponse<AuthResponseDto>.Success(response, ResponseMessages.LoginSuccessResponse);
        }
        #endregion

        #region Change Password
        public async Task<ApiResponse<string>> ChangePasswordAsync(long userId, ChangePasswordRequestDto model)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                return ApiResponse<string>.Failed(ResponseMessages.NotFoundResponse, HttpStatusCode.NotFound);
            }

            var isCurrentPasswordValid = await _userManager.CheckPasswordAsync(user, model.CurrentPassword);
            if (!isCurrentPasswordValid)
            {
                WatchLogger.LogWarning($"Change password attempt with invalid current password for user ID: {userId}", "ChangePasswordAsync");
                return ApiResponse<string>.Failed(ResponseMessages.CurrentPasswordIncorrectResponse, HttpStatusCode.BadRequest);
            }

            if (model.NewPassword != model.ConfirmPassword)
            {
                return ApiResponse<string>.Failed(ResponseMessages.PasswordsDoNotMatchResponse, HttpStatusCode.BadRequest);
            }

            var result = await _userManager.ChangePasswordAsync(user, model.CurrentPassword, model.NewPassword);
            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                WatchLogger.LogWarning($"Change password failed for user ID {userId}: {errors}", "ChangePasswordAsync");
                return ApiResponse<string>.Failed(errors, HttpStatusCode.BadRequest);
            }

            _logger.LogInformation("Password changed successfully for user ID: {UserId}", userId);
            WatchLogger.Log($"Password changed successfully for user ID: {userId}", "ChangePasswordAsync");
            return ApiResponse<string>.Success(ResponseMessages.PasswordChangedSuccessResponse);
        }
        #endregion

        #region Forgot Password - Initaite The Process
        public async Task<ApiResponse<string>> ForgotPasswordAsync(ForgotPasswordRequestDto model)
        {
            var user = await _userManager.FindByEmailAsync(model.Email);
            if (user == null)
            {
                // Don't reveal that the user does not exist
                _logger.LogWarning("Forgot password request for non-existent email: {Email}", model.Email);
                return ApiResponse<string>.Success(ResponseMessages.PasswordResetEmailSentResponse);
            }

            var token = await _userManager.GeneratePasswordResetTokenAsync(user);
            var encodedToken = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(token));

            var emailSent = await _emailService.SendPasswordResetEmailAsync(user.Email, encodedToken, user.FirstName);
            if (!emailSent)
            {
                _logger.LogWarning("Failed to send password reset email to {Email}", user.Email);
                WatchLogger.LogWarning($"Failed to send password reset email to {user.Email}", "ForgotPasswordAsync");
                // Still return success to not reveal user existence
            }
            else
            {
                _logger.LogInformation("Password reset email sent to {Email}", user.Email);
                WatchLogger.Log($"Password reset email sent to {user.Email}", "ForgotPasswordAsync");
            }

            return ApiResponse<string>.Success(ResponseMessages.PasswordResetEmailSentResponse);
        }
        #endregion

        #region Reset Password
        public async Task<ApiResponse<string>> ResetPasswordAsync(ResetPasswordRequestDto model)
        {
            var user = await _userManager.FindByEmailAsync(model.Email);
            if (user == null)
            {
                return ApiResponse<string>.Failed(ResponseMessages.InvalidPasswordResetTokenResponse, HttpStatusCode.BadRequest);
            }

            if (model.NewPassword != model.ConfirmPassword)
            {
                return ApiResponse<string>.Failed(ResponseMessages.PasswordsDoNotMatchResponse, HttpStatusCode.BadRequest);
            }

            var decodedToken = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(model.Token));
            var result = await _userManager.ResetPasswordAsync(user, decodedToken, model.NewPassword);

            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                WatchLogger.LogWarning($"Password reset failed for {model.Email}: {errors}", "ResetPasswordAsync");
                return ApiResponse<string>.Failed(ResponseMessages.InvalidPasswordResetTokenResponse, HttpStatusCode.BadRequest);
            }

            _logger.LogInformation("Password reset successful for {Email}", model.Email);
            WatchLogger.Log($"Password reset successful for {model.Email}", "ResetPasswordAsync");
            return ApiResponse<string>.Success(ResponseMessages.PasswordResetSuccessResponse);
        }
        #endregion

        #region Refresh Token
        public async Task<ApiResponse<AuthResponseDto>> RefreshTokenAsync(RefreshTokenRequestDto model)
        {
            // Check if token is blacklisted
            var isBlacklisted = await _jwtService.IsTokenBlacklisted(model.AccessToken);
            if (isBlacklisted)
            {
                WatchLogger.LogWarning($"Attempt to use blacklisted token", "RefreshTokenAsync");
                return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.UnauthorizedResponse,
                    HttpStatusCode.Unauthorized, ResponseMessages.TokenBlacklistedResponse);
            }

            ClaimsPrincipal principal;
            try
            {
                principal = await _jwtService.GetPrincipalFromExpiredToken(model.AccessToken);
            }
            catch (Exception ex)
            {
                WatchLogger.LogWarning($"Invalid token: {ex.Message}", "RefreshTokenAsync");
                return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.InvalidTokenResponse, HttpStatusCode.BadRequest);
            }

            var userId = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.InvalidTokenResponse, HttpStatusCode.BadRequest);
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.InvalidTokenResponse, HttpStatusCode.BadRequest);
            }

            if (user.RefreshToken != model.RefreshToken)
            {
                WatchLogger.LogWarning($"Invalid refresh token for user ID: {userId}", "RefreshTokenAsync");
                return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.InvalidRefreshTokenResponse, HttpStatusCode.BadRequest);
            }

            if (user.RefreshTokenExpiryTime <= DateTime.UtcNow)
            {
                WatchLogger.LogWarning($"Expired refresh token for user ID: {userId}", "RefreshTokenAsync");
                return ApiResponse<AuthResponseDto>.Failed(ResponseMessages.ExpiredRefreshTokenResponse, HttpStatusCode.BadRequest);
            }

            // Generate new tokens
            var newAccessToken = await _jwtService.GenerateJwtToken(user);
            var newRefreshToken = await _jwtService.GenerateRefreshToken();

            // Update user with new refresh token
            user.RefreshToken = newRefreshToken;
            user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7); // Refresh token valid for 7 days
            await _userManager.UpdateAsync(user);

            // Blacklist the old token
            await _jwtService.BlacklistToken(model.AccessToken);

            var response = _mapper.Map<AuthResponseDto>(user);
            response.Token = newAccessToken;
            response.RefreshToken = newRefreshToken;
            response.RefreshTokenExpiryTime = user.RefreshTokenExpiryTime;

            // Get user's role and add it to the response
            var roles = await _userManager.GetRolesAsync(user);
            response.Role = roles.FirstOrDefault() ?? string.Empty;

            // Get user's permissions and add them to the response
            var permissionsEnum = await _jwtService.GetUserPermissionsEnumAsync(user);
            if (permissionsEnum != null)
                permissionsEnum.ForEach(x =>
                {
                    response.Permissions.Add(x.ToString());
                });

            _logger.LogInformation("Token refreshed successfully for user ID: {UserId} with role: {Role} and {PermissionCount} permissions", userId, response.Role, response.Permissions.Count);
            WatchLogger.Log($"Token refreshed successfully for user ID: {userId} with role: {response.Role} and {response.Permissions.Count} permissions", "RefreshTokenAsync");
            return ApiResponse<AuthResponseDto>.Success(response, ResponseMessages.TokenRefreshedSuccessResponse);
        }
        #endregion

        #region Logout
        public async Task<ApiResponse<string>> LogoutAsync(string token)
        {
            // Blacklist the token
            var result = await _jwtService.BlacklistToken(token);
            if (!result)
            {
                return ApiResponse<string>.Failed(ResponseMessages.GeneralExceptionResponse, HttpStatusCode.InternalServerError);
            }

            WatchLogger.Log($"User logged out successfully", "LogoutAsync");
            return ApiResponse<string>.Success(ResponseMessages.LogoutSuccessResponse);
        }
        #endregion

        #region UAE PASS Authentication
        public async Task<ApiResponse<UaePassAuthUrlResponseDto>> GetUaePassAuthorizationUrlAsync()
        {
            try
            {
                WatchLogger.Log("Generating UAE PASS authorization URL", "GetUaePassAuthorizationUrlAsync");
                var result = await _uaePassService.GenerateAuthorizationUrlAsync();

                if (result.IsSuccess && result.Data != null)
                {
                    WatchLogger.Log("UAE PASS authorization URL generated successfully", "GetUaePassAuthorizationUrlAsync");
                    return ApiResponse<UaePassAuthUrlResponseDto>.Success(result.Data, "Authorization URL generated successfully");
                }

                WatchLogger.LogError("Failed to generate UAE PASS authorization URL", "GetUaePassAuthorizationUrlAsync");
                return ApiResponse<UaePassAuthUrlResponseDto>.Failed("Failed to generate authorization URL", HttpStatusCode.InternalServerError);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating UAE PASS authorization URL");
                WatchLogger.LogError($"Error generating UAE PASS authorization URL: {ex.Message}", "GetUaePassAuthorizationUrlAsync");
                return ApiResponse<UaePassAuthUrlResponseDto>.Failed("Failed to generate authorization URL", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<AuthResponseDto>> HandleUaePassCallbackAsync(UaePassCallbackRequestDto model)
        {
            try
            {
                WatchLogger.Log($"Processing UAE PASS callback", "HandleUaePassCallbackAsync");

                // Check for error in callback
                if (!string.IsNullOrEmpty(model.Error))
                {
                    WatchLogger.LogWarning($"UAE PASS callback error: {model.Error} - {model.ErrorDescription}", "HandleUaePassCallbackAsync");
                    return ApiResponse<AuthResponseDto>.Failed($"UAE PASS authentication failed: {model.ErrorDescription ?? model.Error}", HttpStatusCode.BadRequest);
                }

                // Exchange code for token
                var tokenResult = await _uaePassService.ExchangeCodeForTokenAsync(model.Code, model.State);
                if (!tokenResult.IsSuccess || tokenResult.Data == null)
                {
                    WatchLogger.LogError("Failed to exchange UAE PASS code for token", "HandleUaePassCallbackAsync");
                    return ApiResponse<AuthResponseDto>.Failed("Failed to authenticate with UAE PASS", HttpStatusCode.BadRequest);
                }

                // Get user profile
                var profileResult = await _uaePassService.GetUserProfileAsync(tokenResult.Data.AccessToken);
                if (!profileResult.IsSuccess || profileResult.Data == null)
                {
                    WatchLogger.LogError("Failed to fetch user profile from UAE PASS", "HandleUaePassCallbackAsync");
                    return ApiResponse<AuthResponseDto>.Failed("Failed to fetch user profile", HttpStatusCode.BadRequest);
                }

                var uaePassProfile = profileResult.Data;

                // Find or create user
                var user = await FindOrCreateUaePassUserAsync(uaePassProfile);
                if (user == null)
                {
                    WatchLogger.LogError("Failed to find or create user from UAE PASS profile", "HandleUaePassCallbackAsync");
                    return ApiResponse<AuthResponseDto>.Failed("Failed to process user authentication", HttpStatusCode.InternalServerError);
                }

                // Update UAE PASS specific fields
                user.UaePassUserId = uaePassProfile.Subject;
                user.UaePassUuid = uaePassProfile.Uuid;
                user.IsUaePassVerified = true;
                user.UaePassLastLogin = DateTime.UtcNow;
                user.EmailConfirmed = true; // UAE PASS users are considered verified

                await _userManager.UpdateAsync(user);

                // Generate JWT token
                var token = await _jwtService.GenerateJwtToken(user);
                var refreshToken = await _jwtService.GenerateRefreshToken();

                user.RefreshToken = refreshToken;
                user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);
                await _userManager.UpdateAsync(user);

                var response = _mapper.Map<AuthResponseDto>(user);
                response.Token = token;
                response.RefreshToken = refreshToken;
                response.RefreshTokenExpiryTime = user.RefreshTokenExpiryTime;

                // Get user's role and add it to the response
                var roles = await _userManager.GetRolesAsync(user);
                response.Role = roles.FirstOrDefault() ?? string.Empty;

                // Get user's permissions and add them to the response
                var permissionsEnum = await _jwtService.GetUserPermissionsEnumAsync(user);
                if (permissionsEnum != null)
                    permissionsEnum.ForEach(x =>
                    {
                        response.Permissions.Add(x.ToString());
                    });

                WatchLogger.Log($"UAE PASS authentication successful for user: {user.Email}", "HandleUaePassCallbackAsync");
                return ApiResponse<AuthResponseDto>.Success(response, "UAE PASS authentication successful");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing UAE PASS callback");
                WatchLogger.LogError($"Error processing UAE PASS callback: {ex.Message}", "HandleUaePassCallbackAsync");
                return ApiResponse<AuthResponseDto>.Failed("Failed to process UAE PASS authentication", HttpStatusCode.InternalServerError);
            }
        }
        #endregion

        #region Private Helper Methods
        private async Task<IdentityResult> AddUserToRoleAsync(User user, RoleEnums.SystemRoles role)
        {
            var roleName = role.ToString();

            // Check if role exists, if not create it
            var roleExists = await _roleManager.RoleExistsAsync(roleName);
            if (!roleExists)
            {
                var createRoleResult = await _roleManager.CreateAsync(new Role
                {
                    Name = roleName,
                    Description = $"System role for {roleName}",
                    IsEditable = false,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });
                if (!createRoleResult.Succeeded)
                {
                    _logger.LogError("Failed to create role {RoleName}: {Errors}", roleName,
                        string.Join(", ", createRoleResult.Errors.Select(e => e.Description)));
                    return createRoleResult;
                }
            }

            // Add user to role
            var result = await _userManager.AddToRoleAsync(user, roleName);
            if (result.Succeeded)
            {
                _logger.LogInformation("User {Email} added to role {RoleName}", user.Email, roleName);
                WatchLogger.Log($"User {user.Email} added to role {roleName}", "AddUserToRoleAsync");
            }
            else
            {
                _logger.LogError("Failed to add user {Email} to role {RoleName}: {Errors}",
                    user.Email, roleName, string.Join(", ", result.Errors.Select(e => e.Description)));
                WatchLogger.LogError($"Failed to add user {user.Email} to role {roleName}: {string.Join(", ", result.Errors.Select(e => e.Description))}", "AddUserToRoleAsync");
            }

            return result;
        }

        private async Task<User?> FindOrCreateUaePassUserAsync(UaePassUserProfileDto uaePassProfile)
        {
            try
            {
                User? user = null;

                // First, try to find user by Emirates ID if available
                if (!string.IsNullOrEmpty(uaePassProfile.EmiratesId))
                {
                    user = await _userManager.Users.FirstOrDefaultAsync(u => u.EmiratesId == uaePassProfile.EmiratesId);
                    if (user != null)
                    {
                        WatchLogger.Log($"Found existing user by Emirates ID: {uaePassProfile.EmiratesId}", "FindOrCreateUaePassUserAsync");
                        return user;
                    }
                }

                // Then try to find by UAE PASS User ID
                if (!string.IsNullOrEmpty(uaePassProfile.Subject))
                {
                    user = await _userManager.Users.FirstOrDefaultAsync(u => u.UaePassUserId == uaePassProfile.Subject);
                    if (user != null)
                    {
                        WatchLogger.Log($"Found existing user by UAE PASS User ID: {uaePassProfile.Subject}", "FindOrCreateUaePassUserAsync");
                        return user;
                    }
                }

                // Finally, try to find by email if available
                if (!string.IsNullOrEmpty(uaePassProfile.Email))
                {
                    user = await _userManager.FindByEmailAsync(uaePassProfile.Email);
                    if (user != null)
                    {
                        WatchLogger.Log($"Found existing user by email: {uaePassProfile.Email}", "FindOrCreateUaePassUserAsync");
                        return user;
                    }
                }

                // If no user found, create a new one
                var newUser = new User
                {
                    FirstName = uaePassProfile.FirstNameEn ?? "Unknown",
                    LastName = uaePassProfile.LastNameEn ?? "User",
                    Email = uaePassProfile.Email ?? $"uaepass_{uaePassProfile.Subject}@temp.local",
                    UserName = uaePassProfile.Email ?? $"uaepass_{uaePassProfile.Subject}@temp.local",
                    PhoneNumber = uaePassProfile.Mobile,
                    Gender = uaePassProfile.Gender,
                    EmiratesId = uaePassProfile.EmiratesId,
                    UaePassUserId = uaePassProfile.Subject,
                    UaePassUuid = uaePassProfile.Uuid,
                    IsUaePassVerified = true,
                    EmailConfirmed = true,
                    IsActive = true,
                    UserType = Common.Enums.AuthEnums.UserTypes.Customer,
                    CreatedOn = DateTime.UtcNow,
                    Address = uaePassProfile.Address
                };

                var result = await _userManager.CreateAsync(newUser);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogError("Failed to create UAE PASS user: {Errors}", errors);
                    WatchLogger.LogError($"Failed to create UAE PASS user: {errors}", "FindOrCreateUaePassUserAsync");
                    return null;
                }

                // Add user to Customer role
                var roleResult = await AddUserToRoleAsync(newUser, RoleEnums.SystemRoles.Customer);
                if (!roleResult.Succeeded)
                {
                    _logger.LogWarning("Failed to add UAE PASS user to Customer role: {Email}", newUser.Email);
                    WatchLogger.LogWarning($"Failed to add UAE PASS user to Customer role: {newUser.Email}", "FindOrCreateUaePassUserAsync");
                }

                return newUser;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding or creating UAE PASS user");
                WatchLogger.LogError(ex.ToString(), "Error finding or creating UAE PASS user", "FindOrCreateUaePassUserAsync");
                return null;
            }
        }
        #endregion
    }
}
