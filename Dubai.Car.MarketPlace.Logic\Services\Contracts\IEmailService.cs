﻿namespace Dubai.Car.MarketPlace.Logic.Services.Contracts
{
    public interface IEmailService
    {
        /// <summary>
        /// Sends email out
        /// </summary>
        /// <param name="to"></param>
        /// <param name="subject"></param>
        /// <param name="body"></param>
        /// <param name="isHtml"></param>
        /// <returns></returns>
        Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true);

        /// <summary>
        /// Sends an account verification email to the specified recipient.
        /// </summary>
        /// <remarks>This method sends an email containing a verification link to the specified recipient.
        /// The email includes the provided <paramref name="verificationToken"/> and is personalized using the
        /// recipient's <paramref name="firstName"/>. Ensure that the email address provided in <paramref name="to"/> is
        /// valid and reachable.</remarks>
        /// <param name="to">The email address of the recipient. This parameter cannot be null or empty.</param>
        /// <param name="verificationToken">The unique token used to verify the recipient's account. This parameter cannot be null or empty.</param>
        /// <param name="firstName">The first name of the recipient, used for personalization in the email. This parameter cannot be null or
        /// empty.</param>
        /// <returns>A task that represents the asynchronous operation. The task result is <see langword="true"/> if the email
        /// was sent successfully; otherwise, <see langword="false"/>.</returns>
        Task<bool> SendAccountVerificationEmailAsync(string? to, string verificationToken, string firstName);

        /// <summary>
        /// Sends a password reset email to the specified recipient.
        /// </summary>
        /// <remarks>This method performs an asynchronous operation to send a password reset email. Ensure
        /// that the provided email address and token  are valid before calling this method. The method does not
        /// guarantee delivery of the email, as it depends on external factors  such as the recipient's email
        /// server.</remarks>
        /// <param name="to">The email address of the recipient. Cannot be null or empty.</param>
        /// <param name="resetToken">The unique token used to reset the password. This token must be valid and not expired.</param>
        /// <param name="firstName">The first name of the recipient, used for personalization in the email. Cannot be null or empty.</param>
        /// <returns>A task that represents the asynchronous operation. The task result is <see langword="true"/> if the email
        /// was sent successfully;  otherwise, <see langword="false"/>.</returns>
        Task<bool> SendPasswordResetEmailAsync(string to, string resetToken, string firstName);
        
        /// <summary>
        /// Send a confirmation email to a vendor after their application is submitted
        /// </summary>
        /// <param name="to">Vendor's email address</param>
        /// <param name="businessName">Name of the vendor's business</param>
        /// <param name="applicationId">ID of the submitted application</param>
        /// <returns>Whether the email was sent successfully</returns>
        Task<bool> SendVendorApplicationConfirmationEmailAsync(string to, string businessName, long applicationId);
        
        /// <summary>
        /// Send approval notification to a vendor with their temporary credentials
        /// </summary>
        /// <param name="to">Vendor's email address</param>
        /// <param name="businessName">Name of the vendor's business</param>
        /// <param name="username">Temporary username</param>
        /// <param name="password">Temporary password</param>
        /// <returns>Whether the email was sent successfully</returns>
        Task<bool> SendVendorApprovalEmailAsync(string to, string businessName, string username, string password);
        
        /// <summary>
        /// Send rejection notification to a vendor
        /// </summary>
        /// <param name="to">Vendor's email address</param>
        /// <param name="businessName">Name of the vendor's business</param>
        /// <param name="rejectionReason">Reason for rejection</param>
        /// <returns>Whether the email was sent successfully</returns>
        Task<bool> SendVendorRejectionEmailAsync(string to, string businessName, string? rejectionReason);
        
        /// <summary>
        /// Send additional information request to a vendor
        /// </summary>
        /// <param name="to">Vendor's email address</param>
        /// <param name="businessName">Name of the vendor's business</param>
        /// <param name="requestDetails">Details of the additional information needed</param>
        /// <returns>Whether the email was sent successfully</returns>
        Task<bool> SendAdditionalInfoRequestEmailAsync(string to, string businessName, string requestDetails);
        
        /// <summary>
        /// Send welcome email to a newly created admin user with temporary credentials
        /// </summary>
        /// <param name="to">Admin user's email address</param>
        /// <param name="firstName">Admin user's first name</param>
        /// <param name="lastName">Admin user's last name</param>
        /// <param name="username">Username for login</param>
        /// <param name="temporaryPassword">Temporary password</param>
        /// <param name="roleName">Role assigned to the admin user</param>
        /// <returns>Whether the email was sent successfully</returns>
        Task<bool> SendAdminWelcomeEmailAsync(string to, string firstName, string lastName, string username, string temporaryPassword, string roleName);
    }
}
