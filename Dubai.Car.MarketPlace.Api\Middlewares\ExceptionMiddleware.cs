﻿using Dubai.Car.MarketPlace.Common.DTOs.Responses.User;
using Dubai.Car.MarketPlace.Common.Utility;
using System.Net;
using System.Text.Json;
using WatchDog;

namespace Dubai.Car.MarketPlace.Api.Middlewares
{
    /// <summary>
    /// ExceptionMiddleware class
    /// </summary>
    public class ExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ExceptionMiddleware> _logger;
        private readonly IHostEnvironment _env;

        /// <summary>
        /// ExceptionMiddleware constructor
        /// </summary>
        /// <param name="next"></param>
        /// <param name="logger"></param>
        /// <param name="env"></param>
        public ExceptionMiddleware(RequestDelegate next, ILogger<ExceptionMiddleware> logger, IHostEnvironment env)
        {
            _next = next;
            _logger = logger;
            _env = env;
        }

        /// <summary>
        /// Method to invoke our exception
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                // generate an 15 digit random alphanumeric string all in capital letters
                var randomString = Guid.NewGuid().ToString().Replace("-", "").Substring(0, 15).ToUpper();

                _logger.LogError(ex, $"Exception occured: {randomString}");
                WatchLogger.LogError(ex.ToString(), ex.TargetSite?.Name, $"Exception occured: {randomString}");
                context.Response.ContentType = "application/json";
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

                var response = _env.IsDevelopment()
                    ? new ApiResponse<string>
                    {
                        StatusCode = HttpStatusCode.InternalServerError,
                        Message = ResponseMessages.GeneralExceptionResponse,
                        DevMessage = ex.ToString(),
                        ExceptionStackTrace = ex.StackTrace,
                        IsSuccess = false,
                        ErrorCode = "500"
                    }
                    : new ApiResponse<string>
                    {
                        StatusCode = HttpStatusCode.InternalServerError,
                        DevMessage = ex.ToString() ?? $"Exception occured with trace id: {randomString}",
                        Message = ResponseMessages.GeneralExceptionResponse,
                        IsSuccess = false,
                        ErrorCode = "500"
                    };

                var options = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
                var json = JsonSerializer.Serialize(response, options);

                await context.Response.WriteAsync(json);
            }
        }
    }
}
