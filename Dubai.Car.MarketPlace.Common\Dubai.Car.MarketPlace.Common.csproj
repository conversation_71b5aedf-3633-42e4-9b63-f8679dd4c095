<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
		<PackageReference Include="Microsoft.Extensions.Primitives" Version="8.0.0" />
		<PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="7.1.2" />
		<PackageReference Include="Microsoft.IdentityModel.Tokens" Version="7.1.2" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.1.2" />
	</ItemGroup>

</Project>
