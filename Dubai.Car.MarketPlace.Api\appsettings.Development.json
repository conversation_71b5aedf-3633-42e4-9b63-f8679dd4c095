{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"ConnectionString": "Server=dpg-d1uvnm6r433s73f66pg0-a.oregon-postgres.render.com;Port=5432;User Id=*****;Password=********************************;Database=AutoMarket-Dev", "LocalConnectionString": "Host=localhost;Port=5432;Database=AutoMarket;Username=postgres;Password=*****", "Redis": "localhost:6379"}, "Cache": {"EnableCaching": false, "DefaultExpirationMinutes": 30, "MaxCacheSizeMB": 100, "EvictionPercentage": 25, "EnableCacheWarming": true, "CacheWarmingIntervalMinutes": 60, "PaymentDataPrewarmDays": 7}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "<EMAIL>", "SmtpPassword": "csnmfmcqwgadndyz", "SenderEmail": "<EMAIL>", "SenderName": "Psyvids System"}, "AwsSettings": {"AccessKey": "********************", "SecretKey": "fBR+W3bXHD+obmuyVzBOc5EkM8TgC9P0SHQWxlzT", "Region": "eu-west-2", "BucketName": "possap-ccdb", "PresignedUrlExpiryMinutes": 600, "MaxFileSizeMB": 10}, "WatchDogCredentials": {"Username": "*****", "Password": "*****"}, "EmailConfig": {"Provider": "SMTP"}, "MailGunConfig": {"Domain": "email.idec.gov.ng", "ApiKey": "************************************", "FromEmail": "<EMAIL>"}, "SmtpConfig": {"Host": "smtp.gmail.com", "Port": 587, "EnableSsl": true, "Username": "<EMAIL>", "Password": "csnmfmcqwgadndyz", "FromEmail": "<EMAIL>", "FromName": "School Payment System"}, "AppConfig": {"BaseUrl": "https://schoolpaymetsystem.onrender.com", "FrontEndUrl": "https://schoolkit-frontend.vercel.app/", "SeedDataOnStartup": true, "EnableHealthCheckMonitoring": true}, "HealthChecks-UI": {"HealthChecks": [{"Name": "School Payment System API", "Uri": "https://schoolpaymetsystem.onrender.com/health"}], "EvaluationTimeInSeconds": 60, "MinimumSecondsBetweenFailureNotifications": 300}, "Hangfire": {"AllowedEnvironment": "Development", "RequireAuthentication": false, "WorkerCount": 20, "DashboardPath": "/hangfire"}, "JwtCredentials": {"SecretKey": "070638cdc8-ccc3e4-4300aa-ba8ddd-f9a70b84d380", "Issuer": "school-payment-system-api", "Audience": "http://localhost:5000", "Lifetime": 60}, "UaePassSettings": {"ClientId": "your-uae-pass-client-id", "ClientSecret": "your-uae-pass-client-secret", "AuthorizationEndpoint": "https://stg-id.uaepass.ae/idshub/authorize", "TokenEndpoint": "https://stg-id.uaepass.ae/idshub/token", "UserInfoEndpoint": "https://stg-id.uaepass.ae/idshub/userinfo", "RedirectUri": "http://localhost:5094/api/auth/uaepass/callback", "Scope": "urn:uae:digitalid:profile:general", "IsSandbox": true, "TimeoutSeconds": 30}, "IpRateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 30}, {"Endpoint": "*", "Period": "10s", "Limit": 10}]}, "IpRateLimitPolicies": {"IpRules": []}, "RateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 60}, {"Endpoint": "post:/api/auth/*", "Period": "15m", "Limit": 5}, {"Endpoint": "get:/api/*", "Period": "1s", "Limit": 10}]}}